refactor this component :
src/pages/DashboardPage.tsx

i want to get data from database and show to dashboard.
this is database schema.

Database Schema
PostgreSQL database with 6 main tables, all with soft delete support and automatic timestamps.
users
Table
Customer information

id, name, created_at, updated_at, deleted_at
products
Table
Menu items (Japanese dishes)

id, name, price, category, created_at, updated_at, deleted_at
orders
Table
Order records with payment status

id, user_id, total_price, is_paid, order_date, created_at, updated_at, deleted_at
order_items
Table
Individual items within orders

id, order_id, product_id, quantity, created_at, updated_at, deleted_at
order_history
Table
Complete audit trail

id, user_name, action_type, order_id, item_name, item_quantity, total_amount, description, created_at, updated_at, deleted_at
debts
Table
Customer debt tracking

id, user_id, amount, created_at, updated_at, deleted_at